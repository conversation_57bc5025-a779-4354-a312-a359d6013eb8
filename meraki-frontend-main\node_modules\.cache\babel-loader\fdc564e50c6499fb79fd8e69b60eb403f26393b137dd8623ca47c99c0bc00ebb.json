{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\DayWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { Avatar, Box, Card, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, LinearProgress } from \"@mui/material\";\nimport { format, parseISO } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst formatTime = timeStr => {\n  if (!timeStr || timeStr === \"--\") {\n    return \"--\";\n  }\n  const minutes = parseInt(timeStr, 10); // 👈 radix added\n  if (isNaN(minutes)) {\n    return timeStr;\n  }\n  const hours = Math.floor(minutes / 60);\n  const mins = minutes % 60;\n  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n};\nconst DayWorkReportFull = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n\n  // Get the entire Activity state for debugging (note: lowercase 'activity' in store)\n  const activityState = useSelector(state => state.activity || {});\n  const {\n    multiUserActivityArr = [],\n    activityArr: legacyActivityArr = []\n  } = activityState;\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Debug logging for state\n  console.log(\"DayWorkReport - Full Activity State:\", activityState);\n  console.log(\"DayWorkReport - multiUserActivityArr:\", multiUserActivityArr);\n  console.log(\"DayWorkReport - legacyActivityArr:\", legacyActivityArr);\n\n  // Format the selected date for display\n  const displayDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? format(parseISO(dateRange.startDate), \"EEE, MMM d, yyyy\") : format(new Date(), \"EEE, MMM d, yyyy\");\n\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\n  // useEffect(() => {\n  //   if (dateRange?.startDate && dateRange?.endDate) {\n  //     dispatch(ActivityActions.getUserActivity({\n  //       startDate: dateRange.startDate,\n  //       endDate: dateRange.endDate,\n  //       view: 'day'\n  //     }));\n  //   }\n  // }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Daily Work Report \\u2013 \", displayDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [\"No employee data available for \", displayDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: [/*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Clock In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Clock Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              colSpan: 2,\n              children: \"Entry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              colSpan: 2,\n              children: \"Exit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Time At Work\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Productive Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Focus Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Early\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Late\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Early\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Late\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => {\n            // Parse time string (e.g., \"2h 30m\") to minutes\n            const parseTime = timeStr => {\n              if (!timeStr || timeStr === \"-\" || timeStr === \"--\") return 0;\n              const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n              if (!match || !match.groups) return 0;\n              const hours = parseInt(match.groups.hours, 10);\n              const minutes = parseInt(match.groups.minutes, 10);\n              return hours * 60 + minutes;\n            };\n            const totalWorkMinutes = parseTime(emp.atwork);\n            const productiveMinutes = parseTime(emp.productivitytime);\n\n            // Calculate productivity percentage\n            const progress = totalWorkMinutes > 0 ? Math.min(100, productiveMinutes / totalWorkMinutes * 100) : 0;\n\n            // Determine progress bar color\n            let barColor = \"inherit\";\n            if (progress >= 80) barColor = \"success\";else if (progress >= 50) barColor = \"warning\";else if (progress > 0) barColor = \"error\";\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: getInitials(emp.name || \"\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      children: emp.name || \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.round(progress),\n                  color: barColor,\n                  sx: {\n                    height: 6,\n                    borderRadius: 4,\n                    width: \"120px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.clockin !== \"--\" ? emp.clockin : \"absent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.clockout !== \"--\" ? emp.clockout : \"absent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"green\"\n                },\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"red\"\n                },\n                children: emp.entrylate || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"green\"\n                },\n                children: emp.exitearly || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"red\"\n                },\n                children: emp.exitlate || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.atwork !== \"--\" ? emp.atwork : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.productivitytime !== \"--\" ? emp.productivitytime : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.idletime !== \"--\" ? emp.idletime : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(DayWorkReportFull, \"hiZnCEzd0rXmeg//ZgyBWGYg4Jw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DayWorkReportFull;\nDayWorkReportFull.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default DayWorkReportFull;\nvar _c;\n$RefreshReg$(_c, \"DayWorkReportFull\");", "map": {"version": 3, "names": ["React", "useEffect", "PropTypes", "useSelector", "useDispatch", "Avatar", "Box", "Card", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "LinearProgress", "format", "parseISO", "ActivityActions", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "formatTime", "timeStr", "minutes", "parseInt", "isNaN", "hours", "Math", "floor", "mins", "DayWorkReportFull", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "activityState", "state", "activity", "multiUserActivityArr", "activityArr", "legacyActivityArr", "console", "log", "displayDate", "startDate", "Date", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "align", "colSpan", "emp", "i", "parseTime", "match", "groups", "totalWorkMinutes", "atwork", "productiveMinutes", "productivitytime", "progress", "min", "barColor", "display", "alignItems", "gap", "value", "round", "color", "sx", "height", "borderRadius", "width", "clockin", "clockout", "entrylate", "<PERSON><PERSON>ly", "exitlate", "idletime", "_c", "propTypes", "shape", "string", "endDate", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/DayWorkReport.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport PropTypes from \"prop-types\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Card,\r\n  Typography,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  LinearProgress\r\n} from \"@mui/material\";\r\nimport { format, parseISO } from \"date-fns\";\r\nimport { ActivityActions } from \"../../../../slices/actions\";\r\n\r\nconst getInitials = (name) =>\r\n  name.split(\" \").map((n) => n[0]).join(\"\");\r\n\r\n const formatTime = (timeStr) => {\r\n  if (!timeStr || timeStr === \"--\") { return \"--\" }\r\n\r\n  const minutes = parseInt(timeStr, 10); // 👈 radix added\r\n  if (isNaN(minutes)) { return timeStr }\r\n\r\n  const hours = Math.floor(minutes / 60);\r\n  const mins = minutes % 60;\r\n  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\r\n};\r\n\r\n\r\nconst DayWorkReportFull = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n\r\n  // Get the entire Activity state for debugging (note: lowercase 'activity' in store)\r\n  const activityState = useSelector((state) => state.activity || {});\r\n  const { multiUserActivityArr = [], activityArr: legacyActivityArr = [] } = activityState;\r\n\r\n  // Use multiUserActivityArr for the ActivityTimeline components\r\n  const activityArr = multiUserActivityArr;\r\n\r\n  // Debug logging for state\r\n  console.log(\"DayWorkReport - Full Activity State:\", activityState);\r\n  console.log(\"DayWorkReport - multiUserActivityArr:\", multiUserActivityArr);\r\n  console.log(\"DayWorkReport - legacyActivityArr:\", legacyActivityArr);\r\n\r\n  // Format the selected date for display\r\n  const displayDate = dateRange?.startDate ? format(parseISO(dateRange.startDate), \"EEE, MMM d, yyyy\") : format(new Date(), \"EEE, MMM d, yyyy\");\r\n\r\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\r\n  // useEffect(() => {\r\n  //   if (dateRange?.startDate && dateRange?.endDate) {\r\n  //     dispatch(ActivityActions.getUserActivity({\r\n  //       startDate: dateRange.startDate,\r\n  //       endDate: dateRange.endDate,\r\n  //       view: 'day'\r\n  //     }));\r\n  //   }\r\n  // }, [dateRange, dispatch]);\r\n\r\n  // If data is not available, show placeholder\r\n  if (!activityArr || activityArr.length === 0) {\r\n    return (\r\n      <Box p={3}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          Daily Work Report – {displayDate}\r\n        </Typography>\r\n        <Typography>\r\n          No employee data available for {displayDate}\r\n        </Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box p={3}>\r\n      {/* <Typography variant=\"h6\" gutterBottom>\r\n        Daily Work Report – {displayDate}\r\n      </Typography> */}\r\n      <TableContainer component={Card}>\r\n        <Table>\r\n          <TableHead>\r\n            <TableRow>\r\n              <TableCell><strong>Name</strong></TableCell>\r\n              <TableCell></TableCell>\r\n              <TableCell>Clock In</TableCell>\r\n              <TableCell>Clock Out</TableCell>\r\n              <TableCell align=\"center\" colSpan={2}>Entry</TableCell>\r\n              <TableCell align=\"center\" colSpan={2}>Exit</TableCell>\r\n              <TableCell>Time At Work</TableCell>\r\n              <TableCell>Productive Time</TableCell>\r\n              <TableCell>Focus Time</TableCell>\r\n            </TableRow>\r\n            <TableRow>\r\n              <TableCell />\r\n              <TableCell />\r\n              <TableCell />\r\n              <TableCell />\r\n              <TableCell>Early</TableCell>\r\n              <TableCell>Late</TableCell>\r\n              <TableCell>Early</TableCell>\r\n              <TableCell>Late</TableCell>\r\n              <TableCell />\r\n              <TableCell />\r\n              <TableCell />\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {activityArr.map((emp, i) => {\r\n              // Parse time string (e.g., \"2h 30m\") to minutes\r\n              const parseTime = (timeStr) => {\r\n                if (!timeStr || timeStr === \"-\" || timeStr === \"--\") return 0;\r\n                const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\r\n                if (!match || !match.groups) return 0;\r\n                const hours = parseInt(match.groups.hours, 10);\r\n                const minutes = parseInt(match.groups.minutes, 10);\r\n                return (hours * 60) + minutes;\r\n              };\r\n\r\n              const totalWorkMinutes = parseTime(emp.atwork);\r\n              const productiveMinutes = parseTime(emp.productivitytime);\r\n\r\n              // Calculate productivity percentage\r\n              const progress = totalWorkMinutes > 0 ? Math.min(100, (productiveMinutes / totalWorkMinutes) * 100) : 0;\r\n\r\n              // Determine progress bar color\r\n              let barColor = \"inherit\";\r\n              if (progress >= 80) barColor = \"success\";\r\n              else if (progress >= 50) barColor = \"warning\";\r\n              else if (progress > 0) barColor = \"error\";\r\n              \r\n              return (\r\n                <TableRow key={i}>\r\n                  <TableCell>\r\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                      <Avatar>{getInitials(emp.name || \"\")}</Avatar>\r\n                      <Box>\r\n                        <Typography>{emp.name || \"\"}</Typography>\r\n                      </Box>\r\n                    </Box>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <LinearProgress\r\n                      variant=\"determinate\"\r\n                      value={Math.round(progress)}\r\n                      color={barColor}\r\n                      sx={{ height: 6, borderRadius: 4, width: \"120px\" }}\r\n                    />\r\n                  </TableCell>\r\n                  <TableCell>{emp.clockin !== \"--\" ? emp.clockin : \"absent\"}</TableCell>\r\n                  <TableCell>{emp.clockout !== \"--\" ? emp.clockout : \"absent\"}</TableCell>\r\n                  <TableCell sx={{ color: \"green\" }}>-</TableCell>\r\n                  <TableCell sx={{ color: \"red\" }}>{emp.entrylate || \"-\"}</TableCell>\r\n                  <TableCell sx={{ color: \"green\" }}>{emp.exitearly || \"-\"}</TableCell>\r\n                  <TableCell sx={{ color: \"red\" }}>{emp.exitlate || \"-\"}</TableCell>\r\n                  <TableCell>{emp.atwork !== \"--\" ? emp.atwork : \"-\"}</TableCell>\r\n                  <TableCell>{emp.productivitytime !== \"--\" ? emp.productivitytime : \"-\"}</TableCell>\r\n                  <TableCell>{emp.idletime !== \"--\" ? emp.idletime : \"-\"}</TableCell>\r\n                </TableRow>\r\n              );\r\n            })}\r\n          </TableBody>\r\n        </Table>\r\n      </TableContainer>\r\n    </Box>\r\n  );\r\n};\r\n\r\nDayWorkReportFull.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default DayWorkReportFull;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SAASC,MAAM,EAAEC,QAAQ,QAAQ,UAAU;AAC3C,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE1C,MAAMC,UAAU,GAAIC,OAAO,IAAK;EAC/B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;IAAE,OAAO,IAAI;EAAC;EAEhD,MAAMC,OAAO,GAAGC,QAAQ,CAACF,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC,IAAIG,KAAK,CAACF,OAAO,CAAC,EAAE;IAAE,OAAOD,OAAO;EAAC;EAErC,MAAMI,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,GAAG,EAAE,CAAC;EACtC,MAAMM,IAAI,GAAGN,OAAO,GAAG,EAAE;EACzB,OAAOG,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;AACtD,CAAC;AAGD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoC,aAAa,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC;EAClE,MAAM;IAAEC,oBAAoB,GAAG,EAAE;IAAEC,WAAW,EAAEC,iBAAiB,GAAG;EAAG,CAAC,GAAGL,aAAa;;EAExF;EACA,MAAMI,WAAW,GAAGD,oBAAoB;;EAExC;EACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEP,aAAa,CAAC;EAClEM,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEJ,oBAAoB,CAAC;EAC1EG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,iBAAiB,CAAC;;EAEpE;EACA,MAAMG,WAAW,GAAGX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEY,SAAS,GAAGjC,MAAM,CAACC,QAAQ,CAACoB,SAAS,CAACY,SAAS,CAAC,EAAE,kBAAkB,CAAC,GAAGjC,MAAM,CAAC,IAAIkC,IAAI,CAAC,CAAC,EAAE,kBAAkB,CAAC;;EAE7I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAI,CAACN,WAAW,IAAIA,WAAW,CAACO,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE/B,OAAA,CAACd,GAAG;MAAC8C,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACRjC,OAAA,CAACZ,UAAU;QAAC8C,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,2BAChB,EAACL,WAAW;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACbvC,OAAA,CAACZ,UAAU;QAAA6C,QAAA,GAAC,iCACqB,EAACL,WAAW;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEvC,OAAA,CAACd,GAAG;IAAC8C,CAAC,EAAE,CAAE;IAAAC,QAAA,eAIRjC,OAAA,CAACR,cAAc;MAACgD,SAAS,EAAErD,IAAK;MAAA8C,QAAA,eAC9BjC,OAAA,CAACX,KAAK;QAAA4C,QAAA,gBACJjC,OAAA,CAACP,SAAS;UAAAwC,QAAA,gBACRjC,OAAA,CAACN,QAAQ;YAAAuC,QAAA,gBACPjC,OAAA,CAACT,SAAS;cAAA0C,QAAA,eAACjC,OAAA;gBAAAiC,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvBvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCvC,OAAA,CAACT,SAAS;cAACkD,KAAK,EAAC,QAAQ;cAACC,OAAO,EAAE,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvDvC,OAAA,CAACT,SAAS;cAACkD,KAAK,EAAC,QAAQ;cAACC,OAAO,EAAE,CAAE;cAAAT,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtDvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACXvC,OAAA,CAACN,QAAQ;YAAAuC,QAAA,gBACPjC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BvC,OAAA,CAACT,SAAS;cAAA0C,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbvC,OAAA,CAACT,SAAS;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZvC,OAAA,CAACV,SAAS;UAAA2C,QAAA,EACPT,WAAW,CAACpB,GAAG,CAAC,CAACuC,GAAG,EAAEC,CAAC,KAAK;YAC3B;YACA,MAAMC,SAAS,GAAIrC,OAAO,IAAK;cAC7B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;cAC7D,MAAMsC,KAAK,GAAGtC,OAAO,CAACsC,KAAK,CAAC,mCAAmC,CAAC;cAChE,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE,OAAO,CAAC;cACrC,MAAMnC,KAAK,GAAGF,QAAQ,CAACoC,KAAK,CAACC,MAAM,CAACnC,KAAK,EAAE,EAAE,CAAC;cAC9C,MAAMH,OAAO,GAAGC,QAAQ,CAACoC,KAAK,CAACC,MAAM,CAACtC,OAAO,EAAE,EAAE,CAAC;cAClD,OAAQG,KAAK,GAAG,EAAE,GAAIH,OAAO;YAC/B,CAAC;YAED,MAAMuC,gBAAgB,GAAGH,SAAS,CAACF,GAAG,CAACM,MAAM,CAAC;YAC9C,MAAMC,iBAAiB,GAAGL,SAAS,CAACF,GAAG,CAACQ,gBAAgB,CAAC;;YAEzD;YACA,MAAMC,QAAQ,GAAGJ,gBAAgB,GAAG,CAAC,GAAGnC,IAAI,CAACwC,GAAG,CAAC,GAAG,EAAGH,iBAAiB,GAAGF,gBAAgB,GAAI,GAAG,CAAC,GAAG,CAAC;;YAEvG;YACA,IAAIM,QAAQ,GAAG,SAAS;YACxB,IAAIF,QAAQ,IAAI,EAAE,EAAEE,QAAQ,GAAG,SAAS,CAAC,KACpC,IAAIF,QAAQ,IAAI,EAAE,EAAEE,QAAQ,GAAG,SAAS,CAAC,KACzC,IAAIF,QAAQ,GAAG,CAAC,EAAEE,QAAQ,GAAG,OAAO;YAEzC,oBACEtD,OAAA,CAACN,QAAQ;cAAAuC,QAAA,gBACPjC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,eACRjC,OAAA,CAACd,GAAG;kBAACqE,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAxB,QAAA,gBAC7CjC,OAAA,CAACf,MAAM;oBAAAgD,QAAA,EAAEhC,WAAW,CAAC0C,GAAG,CAACzC,IAAI,IAAI,EAAE;kBAAC;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC9CvC,OAAA,CAACd,GAAG;oBAAA+C,QAAA,eACFjC,OAAA,CAACZ,UAAU;sBAAA6C,QAAA,EAAEU,GAAG,CAACzC,IAAI,IAAI;oBAAE;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,eACRjC,OAAA,CAACL,cAAc;kBACbuC,OAAO,EAAC,aAAa;kBACrBwB,KAAK,EAAE7C,IAAI,CAAC8C,KAAK,CAACP,QAAQ,CAAE;kBAC5BQ,KAAK,EAAEN,QAAS;kBAChBO,EAAE,EAAE;oBAAEC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAQ;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,EAAEU,GAAG,CAACsB,OAAO,KAAK,IAAI,GAAGtB,GAAG,CAACsB,OAAO,GAAG;cAAQ;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtEvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,EAAEU,GAAG,CAACuB,QAAQ,KAAK,IAAI,GAAGvB,GAAG,CAACuB,QAAQ,GAAG;cAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxEvC,OAAA,CAACT,SAAS;gBAACsE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAQ,CAAE;gBAAA3B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChDvC,OAAA,CAACT,SAAS;gBAACsE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAM,CAAE;gBAAA3B,QAAA,EAAEU,GAAG,CAACwB,SAAS,IAAI;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnEvC,OAAA,CAACT,SAAS;gBAACsE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAQ,CAAE;gBAAA3B,QAAA,EAAEU,GAAG,CAACyB,SAAS,IAAI;cAAG;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEvC,OAAA,CAACT,SAAS;gBAACsE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAM,CAAE;gBAAA3B,QAAA,EAAEU,GAAG,CAAC0B,QAAQ,IAAI;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClEvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,EAAEU,GAAG,CAACM,MAAM,KAAK,IAAI,GAAGN,GAAG,CAACM,MAAM,GAAG;cAAG;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/DvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,EAAEU,GAAG,CAACQ,gBAAgB,KAAK,IAAI,GAAGR,GAAG,CAACQ,gBAAgB,GAAG;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnFvC,OAAA,CAACT,SAAS;gBAAA0C,QAAA,EAAEU,GAAG,CAAC2B,QAAQ,KAAK,IAAI,GAAG3B,GAAG,CAAC2B,QAAQ,GAAG;cAAG;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAzBtDK,CAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BN,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACrB,EAAA,CAvIIF,iBAAiB;EAAA,QACJhC,WAAW,EAGND,WAAW;AAAA;AAAAwF,EAAA,GAJ7BvD,iBAAiB;AAyIvBA,iBAAiB,CAACwD,SAAS,GAAG;EAC5BvD,SAAS,EAAEnC,SAAS,CAAC2F,KAAK,CAAC;IACzB5C,SAAS,EAAE/C,SAAS,CAAC4F,MAAM;IAC3BC,OAAO,EAAE7F,SAAS,CAAC4F;EACrB,CAAC;AACH,CAAC;AAED,eAAe1D,iBAAiB;AAAC,IAAAuD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}